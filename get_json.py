# get_full_annotations.py

import json
from pathlib import Path
from kili.client import Kili

# === Configuration ===
API_KEY = "cma6sv06wf8tr015vcumzbjob/835a9982bff39b7bbc862e26cc2e2e10"
PROJECT_ID = "cmb53asi847xh0171abma0b32"
ASSET_NAME = "HydroFORM_Gen2_Demo_2_gr1"
EXPORT_PATH = Path("json") / f"{ASSET_NAME}_combined_from_api.json"

# === Initialisation Kili ===
kili = Kili(api_key=API_KEY)

def get_all_labels(project_id: str, external_id: str):
    labels = kili.labels(
        project_id=project_id,
        asset_external_id_in=[external_id],
        fields=[
            "jsonResponse", "author { email }", "createdAt", "labelType"
        ]
    )
    return labels




def merge_labels(labels):
    result = {}
    for label in labels:
        json_response = label.get("jsonResponse", {})
        for job, frames in json_response.items():
            if job not in result:
                result[job] = {}
            for frame_idx, annotations in frames.items():
                if frame_idx not in result[job]:
                    result[job][frame_idx] = []
                result[job][frame_idx].extend(annotations)
    return result

def save_json(data, path: Path):
    path.parent.mkdir(exist_ok=True, parents=True)
    with open(path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2)
    print(f"[OK] Exporté : {path}")

if __name__ == "__main__":
    print("[INFO] Récupération des labels...")
    all_labels = get_all_labels(PROJECT_ID, ASSET_NAME)
    print(f"[INFO] {len(all_labels)} labels trouvés.")

    combined_json = merge_labels(all_labels)
    save_json(combined_json, EXPORT_PATH)
