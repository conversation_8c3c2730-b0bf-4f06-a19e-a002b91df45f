#!/usr/bin/env python3
"""
Test simple pour récupérer le format complet
"""

print("🔄 Début du test...")

try:
    from utils.kili_export import get_kili_json
    print("✅ Import réussi")
    
    # Configuration
    KILI_API_KEY = 'cma6sv06wf8tr015vcumzbjob/835a9982bff39b7bbc862e26cc2e2e10'
    KILI_PROJECT_ID = 'cmb52fpga1bd8015kewxihgop'
    asset_name = "HydroFORM_Gen2_Demo_2_gr1"
    
    print("🔄 Test du format complet...")
    
    # Test du format complet
    full_data = get_kili_json(
        asset_name=asset_name, 
        kili_project_ID=KILI_PROJECT_ID, 
        kili_api_key=KILI_API_KEY,
        export_format="full_asset"
    )
    
    print("✅ Export réussi !")
    print(f"📁 Fichier créé : ./json/{asset_name}_full.json")
    
    # Vérifications
    if 'latestLabel' in full_data:
        print("✅ latestLabel trouvé")
        if 'jsonResponse' in full_data['latestLabel']:
            print("✅ jsonResponse trouvé")
            json_resp = full_data['latestLabel']['jsonResponse']
            if '0' in json_resp:
                print("✅ Frame 0 trouvée")
                frame_0 = json_resp['0']
                print(f"🔍 Clés dans frame 0: {list(frame_0.keys())}")
            else:
                print("⚠️ Frame 0 non trouvée")
        else:
            print("❌ jsonResponse non trouvé")
    else:
        print("❌ latestLabel non trouvé")

except Exception as e:
    print(f"❌ Erreur : {e}")
    import traceback
    traceback.print_exc()

print("🏁 Fin du test")
