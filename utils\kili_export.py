import os
from kili.client import Kili
import json

def get_all_labels_for_asset(kili, project_id, asset_id):
    """
    Récupère tous les labels pour un asset donné
    """
    try:
        # Méthode 1: Utiliser kili.labels() directement
        all_labels = kili.labels(
            project_id=project_id,
            asset_id=asset_id
        )
        return all_labels
    except Exception as e:
        print(f"⚠️ Erreur avec kili.labels(): {e}")
        # Méthode 2: Fallback - récupérer via assets
        try:
            assets = kili.assets(
                project_id=project_id,
                asset_id=asset_id
            )
            if assets and 'labels' in assets[0]:
                return assets[0]['labels']
        except Exception as e2:
            print(f"⚠️ Erreur avec fallback: {e2}")
        return []

def merge_labels_annotations(labels):
    """
    Fusionne les annotations de plusieurs labels en gardant le maximum d'annotations par frame
    """
    merged_json_response = {}

    for label in labels:
        json_resp = label.get('jsonResponse', {})

        for frame_id, frame_data in json_resp.items():
            if frame_id not in merged_json_response:
                # Première fois qu'on voit cette frame, on la copie entièrement
                merged_json_response[frame_id] = frame_data.copy()
            else:
                # Frame déjà existante, on fusionne les annotations
                existing_frame = merged_json_response[frame_id]

                # Fusionner OBJECT_DETECTION_JOB
                if 'OBJECT_DETECTION_JOB' in frame_data and 'OBJECT_DETECTION_JOB' in existing_frame:
                    new_annotations = frame_data['OBJECT_DETECTION_JOB'].get('annotations', [])
                    existing_annotations = existing_frame['OBJECT_DETECTION_JOB'].get('annotations', [])

                    # Créer un set des IDs existants pour éviter les doublons
                    existing_ids = {ann.get('mid', '') for ann in existing_annotations}

                    # Ajouter les nouvelles annotations qui n'existent pas déjà
                    for ann in new_annotations:
                        ann_id = ann.get('mid', '')
                        if ann_id not in existing_ids:
                            existing_annotations.append(ann)
                            existing_ids.add(ann_id)

                    existing_frame['OBJECT_DETECTION_JOB']['annotations'] = existing_annotations

                # Copier les autres champs s'ils n'existent pas
                for key, value in frame_data.items():
                    if key not in existing_frame:
                        existing_frame[key] = value

    return merged_json_response

def get_kili_json(asset_name, kili_project_ID, kili_api_key, export_format="jsonResponse"):
    """
    Récupère les données d'un asset depuis Kili

    Args:
        asset_name: Nom de l'asset
        kili_project_ID: ID du projet Kili
        kili_api_key: Clé API Kili
        export_format: "jsonResponse" (ancien format) ou "full_asset" (nouveau format complet)
    """
    # Connexion à Kili
    kili = Kili(api_key=kili_api_key)

    # Récupération des assets depuis Kili
    annotated_assets = kili.assets(
        project_id=kili_project_ID,
        external_id_contains=[asset_name, asset_name+'.mp4']
    )

    if not annotated_assets:
        raise ValueError(f"Aucun asset trouvé avec le nom '{asset_name}'")

    asset = annotated_assets[0]
    asset_id = asset['id']

    print(f"🔍 RÉCUPÉRATION COMPLÈTE pour {asset_name} (ID: {asset_id}):")

    # Récupérer TOUS les labels pour cet asset
    all_labels = get_all_labels_for_asset(kili, kili_project_ID, asset_id)

    print(f"   📊 Nombre total de labels trouvés : {len(all_labels)}")

    # Analyser chaque label
    for i, label in enumerate(all_labels):
        print(f"   📝 Label {i+1}:")
        print(f"      📅 Créé le : {label.get('createdAt', 'N/A')}")
        print(f"      👤 Auteur : {label.get('author', {}).get('email', 'N/A')}")
        print(f"      🏷️ Latest pour user : {label.get('isLatestLabelForUser', 'N/A')}")

        # Compter les annotations dans ce label
        json_resp = label.get('jsonResponse', {})
        total_annotations = 0
        for frame_id, frame_data in json_resp.items():
            if 'OBJECT_DETECTION_JOB' in frame_data:
                annotations = frame_data['OBJECT_DETECTION_JOB'].get('annotations', [])
                total_annotations += len(annotations)
        print(f"      📊 Total annotations : {total_annotations}")

    print("=" * 50)

    # Création du dossier json s'il n'existe pas
    os.makedirs("json", exist_ok=True)

    if export_format == "full_asset":
        # Format complet comme celui de l'équipe Kili
        # Stratégie améliorée : Fusionner TOUS les labels pour avoir toutes les annotations
        if not all_labels:
            raise ValueError(f"Aucune annotation trouvée pour l'asset '{asset_name}'")

        print(f"🔄 Fusion de {len(all_labels)} labels...")

        # Fusionner toutes les annotations de tous les labels
        merged_json_response = merge_labels_annotations(all_labels)

        # FILTRER : Ne garder que les frames qui ont des annotations
        filtered_json_response = {}
        total_merged_annotations = 0

        for frame_id, frame_data in merged_json_response.items():
            if 'OBJECT_DETECTION_JOB' in frame_data:
                annotations = frame_data['OBJECT_DETECTION_JOB'].get('annotations', [])
                if annotations:  # Seulement si la frame a des annotations
                    filtered_json_response[frame_id] = frame_data
                    total_merged_annotations += len(annotations)

        print(f"   🎬 Frames avec annotations : {len(filtered_json_response)}")
        print(f"   ✅ Fusion terminée : {total_merged_annotations} annotations totales")

        # Utiliser le label le plus récent pour les métadonnées, mais avec le jsonResponse filtré
        latest_label = max(all_labels, key=lambda x: x.get('createdAt', ''))
        latest_label['jsonResponse'] = filtered_json_response

        # Construction du nom complet de l'auteur
        author_info = latest_label.get('author', {})
        if author_info:
            firstname = author_info.get('firstname', '')
            lastname = author_info.get('lastname', '')
            # Construire le nom complet comme dans le fichier de l'équipe Kili
            full_name = f"{firstname} {lastname}".strip()
            author_info['name'] = full_name

        # Construction de la structure complète
        full_asset_data = {
            "content": asset.get('content', ''),
            "externalId": asset.get('externalId', ''),
            "id": asset.get('id', ''),
            "jsonContent": asset.get('jsonContent'),
            "jsonMetadata": asset.get('jsonMetadata', {}),
            "latestLabel": {
                "author": author_info,
                "createdAt": latest_label.get('createdAt', ''),
                "isLatestLabelForUser": latest_label.get('isLatestLabelForUser', False),
                "isSentBackToQueue": latest_label.get('isSentBackToQueue', False),
                "jsonResponse": latest_label.get('jsonResponse', {})
            }
        }

        output_file = f"./json/{asset_name}_full.json"
        with open(output_file, "w") as outfile:
            json.dump(full_asset_data, outfile, indent=4)

        print(f"Fichier complet {output_file} créé avec succès pour l'asset '{asset_name}'")
        return full_asset_data

    else:
        # Format original (jsonResponse seulement)
        if not asset.get('labels'):
            raise ValueError(f"Aucune annotation trouvée pour l'asset '{asset_name}'")

        last_label = asset['labels'][-1]
        json_annotation = last_label['jsonResponse']

        output_file = f"./json/{asset_name}.json"
        with open(output_file, "w") as outfile:
            json.dump(json_annotation, outfile, indent=4)

        print(f"Fichier {output_file} créé avec succès pour l'asset '{asset_name}'")
        return json_annotation

# if __name__ == "__main__":
    # Exemple d'utilisation
    # get_kili_json(asset_name="HydroFORM_Gen2_Demo_2_gr1")