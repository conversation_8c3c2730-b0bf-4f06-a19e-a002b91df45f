import os
from kili.client import Kili
import json

def get_kili_json(asset_name, kili_project_ID, kili_api_key, export_format="jsonResponse"):
    """
    Récupère les données d'un asset depuis Kili

    Args:
        asset_name: Nom de l'asset
        kili_project_ID: ID du projet Kili
        kili_api_key: Clé API Kili
        export_format: "jsonResponse" (ancien format) ou "full_asset" (nouveau format complet)
    """
    # Connexion à Kili
    kili = Kili(api_key=kili_api_key)

    # Récupération des assets depuis Kili (format simple pour éviter les erreurs GraphQL)
    annotated_assets = kili.assets(
        project_id=kili_project_ID,
        external_id_contains=[asset_name, asset_name+'.mp4']
    )

    if not annotated_assets:
        raise ValueError(f"Aucun asset trouvé avec le nom '{asset_name}'")

    asset = annotated_assets[0]

    # Création du dossier json s'il n'existe pas
    os.makedirs("json", exist_ok=True)

    if export_format == "full_asset":
        # Format complet comme celui de l'équipe Kili
        # Récupération de la dernière annotation
        if not asset.get('labels'):
            raise ValueError(f"Aucune annotation trouvée pour l'asset '{asset_name}'")

        latest_label = asset['labels'][-1]

        # Construction du nom complet de l'auteur
        author_info = latest_label.get('author', {})
        if author_info:
            firstname = author_info.get('firstname', '')
            lastname = author_info.get('lastname', '')
            # Construire le nom complet comme dans le fichier de l'équipe Kili
            full_name = f"{firstname} {lastname}".strip()
            author_info['name'] = full_name

        # Construction de la structure complète
        full_asset_data = {
            "content": asset.get('content', ''),
            "externalId": asset.get('externalId', ''),
            "id": asset.get('id', ''),
            "jsonContent": asset.get('jsonContent'),
            "jsonMetadata": asset.get('jsonMetadata', {}),
            "latestLabel": {
                "author": author_info,
                "createdAt": latest_label.get('createdAt', ''),
                "isLatestLabelForUser": latest_label.get('isLatestLabelForUser', False),
                "isSentBackToQueue": latest_label.get('isSentBackToQueue', False),
                "jsonResponse": latest_label.get('jsonResponse', {})
            }
        }

        # Enregistrer à la fois le format complet ET le format standard
        output_file_full = f"./json/{asset_name}_full.json"
        output_file_standard = f"./json/{asset_name}.json"

        # Sauvegarde du format complet
        with open(output_file_full, "w") as outfile:
            json.dump(full_asset_data, outfile, indent=4)

        # Sauvegarde du jsonResponse seul pour compatibilité
        with open(output_file_standard, "w") as outfile:
            json.dump(latest_label.get('jsonResponse', {}), outfile, indent=4)

        print(f"Fichier complet {output_file_full} créé avec succès")
        print(f"Fichier standard {output_file_standard} créé avec succès")
        return full_asset_data

    else:
        # Format original (jsonResponse seulement)
        if not asset.get('labels'):
            raise ValueError(f"Aucune annotation trouvée pour l'asset '{asset_name}'")

        last_label = asset['labels'][-1]
        json_annotation = last_label['jsonResponse']

        output_file = f"./json/{asset_name}.json"
        with open(output_file, "w") as outfile:
            json.dump(json_annotation, outfile, indent=4)

        print(f"Fichier {output_file} créé avec succès pour l'asset '{asset_name}'")
        return json_annotation

# if __name__ == "__main__":
    # Exemple d'utilisation
    # get_kili_json(asset_name="HydroFORM_Gen2_Demo_2_gr1")