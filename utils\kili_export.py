import os
from kili.client import Kili
import json

def get_kili_json(asset_name, kili_project_ID, kili_api_key, export_format="jsonResponse"):
    """
    Récupère les données d'un asset depuis Kili

    Args:
        asset_name: Nom de l'asset
        kili_project_ID: ID du projet Kili
        kili_api_key: Clé API Kili
        export_format: "jsonResponse" (ancien format) ou "full_asset" (nouveau format complet)
    """
    # Connexion à Kili
    kili = Kili(api_key=kili_api_key)

    # Récupération des assets depuis Kili avec TOUS les labels
    annotated_assets = kili.assets(
        project_id=kili_project_ID,
        external_id_contains=[asset_name, asset_name+'.mp4'],
        # Récupérer TOUS les labels, pas seulement le dernier
        fields=['id', 'externalId', 'content', 'jsonContent', 'jsonMetadata', 'labels']
    )

    if not annotated_assets:
        raise ValueError(f"Aucun asset trouvé avec le nom '{asset_name}'")

    asset = annotated_assets[0]

    # DIAGNOSTIC : Analyser tous les labels disponibles
    print(f"🔍 DIAGNOSTIC pour {asset_name}:")
    print(f"   📊 Nombre total de labels : {len(asset.get('labels', []))}")

    for i, label in enumerate(asset.get('labels', [])):
        print(f"   📝 Label {i+1}:")
        print(f"      📅 Créé le : {label.get('createdAt', 'N/A')}")
        print(f"      👤 Auteur : {label.get('author', {}).get('email', 'N/A')}")
        print(f"      🏷️ Latest pour user : {label.get('isLatestLabelForUser', 'N/A')}")

        # Compter les annotations dans ce label
        json_resp = label.get('jsonResponse', {})
        total_annotations = 0
        for frame_id, frame_data in json_resp.items():
            if 'OBJECT_DETECTION_JOB' in frame_data:
                annotations = frame_data['OBJECT_DETECTION_JOB'].get('annotations', [])
                total_annotations += len(annotations)
                if annotations:
                    print(f"         🎬 Frame {frame_id}: {len(annotations)} annotations")
        print(f"      📊 Total annotations : {total_annotations}")
    print("=" * 50)

    # Création du dossier json s'il n'existe pas
    os.makedirs("json", exist_ok=True)

    if export_format == "full_asset":
        # Format complet comme celui de l'équipe Kili
        # Récupération de la meilleure annotation
        if not asset.get('labels'):
            raise ValueError(f"Aucune annotation trouvée pour l'asset '{asset_name}'")

        # Stratégie 1: Chercher le label avec le plus d'annotations
        best_label = None
        max_annotations = 0

        for label in asset['labels']:
            json_resp = label.get('jsonResponse', {})
            total_annotations = 0
            for frame_data in json_resp.values():
                if 'OBJECT_DETECTION_JOB' in frame_data:
                    annotations = frame_data['OBJECT_DETECTION_JOB'].get('annotations', [])
                    total_annotations += len(annotations)

            if total_annotations > max_annotations:
                max_annotations = total_annotations
                best_label = label
                print(f"   🏆 Nouveau meilleur label trouvé : {total_annotations} annotations")

        if best_label is None:
            # Fallback: prendre le dernier label
            best_label = asset['labels'][-1]
            print(f"   ⚠️ Aucun label avec annotations, utilisation du dernier")

        latest_label = best_label
        print(f"   ✅ Label sélectionné avec {max_annotations} annotations totales")

        # Construction du nom complet de l'auteur
        author_info = latest_label.get('author', {})
        if author_info:
            firstname = author_info.get('firstname', '')
            lastname = author_info.get('lastname', '')
            # Construire le nom complet comme dans le fichier de l'équipe Kili
            full_name = f"{firstname} {lastname}".strip()
            author_info['name'] = full_name

        # Construction de la structure complète
        full_asset_data = {
            "content": asset.get('content', ''),
            "externalId": asset.get('externalId', ''),
            "id": asset.get('id', ''),
            "jsonContent": asset.get('jsonContent'),
            "jsonMetadata": asset.get('jsonMetadata', {}),
            "latestLabel": {
                "author": author_info,
                "createdAt": latest_label.get('createdAt', ''),
                "isLatestLabelForUser": latest_label.get('isLatestLabelForUser', False),
                "isSentBackToQueue": latest_label.get('isSentBackToQueue', False),
                "jsonResponse": latest_label.get('jsonResponse', {})
            }
        }

        output_file = f"./json/{asset_name}_full.json"
        with open(output_file, "w") as outfile:
            json.dump(full_asset_data, outfile, indent=4)

        print(f"Fichier complet {output_file} créé avec succès pour l'asset '{asset_name}'")
        return full_asset_data

    else:
        # Format original (jsonResponse seulement)
        if not asset.get('labels'):
            raise ValueError(f"Aucune annotation trouvée pour l'asset '{asset_name}'")

        last_label = asset['labels'][-1]
        json_annotation = last_label['jsonResponse']

        output_file = f"./json/{asset_name}.json"
        with open(output_file, "w") as outfile:
            json.dump(json_annotation, outfile, indent=4)

        print(f"Fichier {output_file} créé avec succès pour l'asset '{asset_name}'")
        return json_annotation

# if __name__ == "__main__":
    # Exemple d'utilisation
    # get_kili_json(asset_name="HydroFORM_Gen2_Demo_2_gr1")