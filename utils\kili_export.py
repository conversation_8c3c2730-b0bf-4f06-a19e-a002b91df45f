import os
from kili.client import <PERSON><PERSON>
import json

def get_kili_json(asset_name, kili_project_ID, kili_api_key):
    # Connexion à Kili
    kili = Kili(api_key=kili_api_key)
    
    # Récupération des assets depuis Kili
    annotated_assets = kili.assets(project_id=kili_project_ID, 
                                   external_id_contains=[asset_name, asset_name+'.mp4'])
    
    # Récupération de la dernière annotation
    last_label = annotated_assets[0]['labels'][-1]
    json_annotation = last_label['jsonResponse']
    
    # Création du dossier json s'il n'existe pas
    os.makedirs("json", exist_ok=True)
    
    # Sauvegarde dans un fichier JSON avec le nom de l'asset dans le dossier json
    output_file = f"./json/{asset_name}.json"
    with open(output_file, "w") as outfile:
        json.dump(json_annotation, outfile, indent=4)
    
    print(f"Fichier {output_file} créé avec succès pour l'asset '{asset_name}'")
    return json_annotation

# if __name__ == "__main__":
    # Exemple d'utilisation
    # get_kili_json(asset_name="HydroFORM_Gen2_Demo_2_gr1")