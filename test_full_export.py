#!/usr/bin/env python3
"""
Script de test pour récupérer le format complet d'asset depuis Kili
"""

from utils.kili_export import get_kili_json

# Configuration Kili
KILI_API_KEY = 'cma6sv06wf8tr015vcumzbjob/835a9982bff39b7bbc862e26cc2e2e10'
KILI_PROJECT_ID = 'cmb52fpga1bd8015kewxihgop'
asset_name = "HydroFORM_Gen2_Demo_2_gr1"

print("🔄 Test de récupération du format complet...")

try:
    # Test du nouveau format complet
    full_data = get_kili_json(
        asset_name=asset_name, 
        kili_project_ID=KILI_PROJECT_ID, 
        kili_api_key=KILI_API_KEY,
        export_format="full_asset"
    )
    
    print("✅ Export complet réussi !")
    print(f"📁 Fichier créé : ./json/{asset_name}_full.json")
    
    # Vérification de la structure
    if 'latestLabel' in full_data and 'jsonResponse' in full_data['latestLabel']:
        print("✅ Structure latestLabel.jsonResponse trouvée")
        
        json_response = full_data['latestLabel']['jsonResponse']
        if '0' in json_response and 'OBJECT_DETECTION_JOB' in json_response['0']:
            annotations = json_response['0']['OBJECT_DETECTION_JOB'].get('annotations', [])
            print(f"✅ {len(annotations)} annotations trouvées dans la frame 0")
            
            # Vérification des champs supplémentaires
            if 'ANNOTATION_JOB_COUNTER' in json_response['0']:
                print("✅ ANNOTATION_JOB_COUNTER trouvé")
            else:
                print("⚠️ ANNOTATION_JOB_COUNTER manquant")
                
            if 'ANNOTATION_NAMES_JOB' in json_response['0']:
                print("✅ ANNOTATION_NAMES_JOB trouvé")
            else:
                print("⚠️ ANNOTATION_NAMES_JOB manquant")
        else:
            print("❌ Structure d'annotations non trouvée")
    else:
        print("❌ Structure latestLabel.jsonResponse non trouvée")
        
    # Comparaison avec le fichier de l'équipe Kili
    print("\n📊 Comparaison avec le fichier de l'équipe Kili...")
    import json
    try:
        with open(f"./json/{asset_name}_from_email.json", "r") as f:
            email_data = json.load(f)
        
        # Comparaison des structures principales
        our_keys = set(full_data.keys())
        email_keys = set(email_data.keys())
        
        print(f"🔑 Clés dans notre export : {our_keys}")
        print(f"🔑 Clés dans le fichier email : {email_keys}")
        
        missing_keys = email_keys - our_keys
        if missing_keys:
            print(f"⚠️ Clés manquantes dans notre export : {missing_keys}")
        else:
            print("✅ Toutes les clés principales sont présentes")
            
    except FileNotFoundError:
        print("⚠️ Fichier de comparaison non trouvé")

except Exception as e:
    print(f"❌ Erreur lors de l'export : {e}")
    import traceback
    traceback.print_exc()
