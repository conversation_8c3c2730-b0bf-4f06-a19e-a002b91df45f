#!/usr/bin/env python3
"""
Test pour diagnostiquer le problème des annotations manquantes
"""

print("🔍 Diagnostic des annotations manquantes...")

try:
    from utils.kili_export import get_kili_json
    
    # Configuration
    KILI_API_KEY = 'cma6sv06wf8tr015vcumzbjob/835a9982bff39b7bbc862e26cc2e2e10'
    KILI_PROJECT_ID = 'cmb52fpga1bd8015kewxihgop'
    asset_name = "HydroFORM_Gen2_Demo_2_gr1"
    
    print("📥 Export avec diagnostic complet...")
    
    # Export avec la nouvelle logique
    full_data = get_kili_json(
        asset_name=asset_name, 
        kili_project_ID=KILI_PROJECT_ID, 
        kili_api_key=KILI_API_KEY,
        export_format="full_asset"
    )
    
    print("✅ Export terminé !")
    
    # Analyse du résultat
    json_resp = full_data['latestLabel']['jsonResponse']
    
    print("\n📊 Analyse du résultat :")
    for frame_id in sorted(json_resp.keys()):
        frame_data = json_resp[frame_id]
        if 'OBJECT_DETECTION_JOB' in frame_data:
            annotations = frame_data['OBJECT_DETECTION_JOB'].get('annotations', [])
            print(f"   🎬 Frame {frame_id}: {len(annotations)} annotations")
        else:
            print(f"   🎬 Frame {frame_id}: Pas d'OBJECT_DETECTION_JOB")
    
    print(f"\n📁 Nouveau fichier créé : ./json/{asset_name}_full.json")
    print("🔄 Maintenant, relancez compare_json.py pour voir la différence !")

except Exception as e:
    print(f"❌ Erreur : {e}")
    import traceback
    traceback.print_exc()
