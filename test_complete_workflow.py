#!/usr/bin/env python3
"""
Test complet du workflow avec le format full_asset
"""

import json
import os

print("🔄 Test du workflow complet...")

# Configuration
asset_name = "HydroFORM_Gen2_Demo_2_gr1"

try:
    # 1. Test de l'export avec le format complet
    print("📥 1. Export depuis Kili (format complet)...")
    from utils.kili_export import get_kili_json
    
    KILI_API_KEY = 'cma6sv06wf8tr015vcumzbjob/835a9982bff39b7bbc862e26cc2e2e10'
    KILI_PROJECT_ID = 'cmb52fpga1bd8015kewxihgop'
    
    full_data = get_kili_json(
        asset_name=asset_name, 
        kili_project_ID=KILI_PROJECT_ID, 
        kili_api_key=KILI_API_KEY,
        export_format="full_asset"
    )
    
    print("✅ Export réussi")
    
    # 2. Vérification de la structure
    print("🔍 2. Vérification de la structure...")
    
    if 'latestLabel' in full_data and 'jsonResponse' in full_data['latestLabel']:
        json_response = full_data['latestLabel']['jsonResponse']
        print("✅ Structure latestLabel.jsonResponse trouvée")
        
        # Vérification des frames
        frames = list(json_response.keys())
        print(f"📊 Frames trouvées : {frames}")
        
        if '0' in json_response:
            frame_0 = json_response['0']
            print(f"🔍 Contenu frame 0 : {list(frame_0.keys())}")
            
            # Vérification des champs spéciaux
            if 'ANNOTATION_JOB_COUNTER' in frame_0:
                print(f"✅ ANNOTATION_JOB_COUNTER : {frame_0['ANNOTATION_JOB_COUNTER']}")
            else:
                print("⚠️ ANNOTATION_JOB_COUNTER manquant")
                
            if 'ANNOTATION_NAMES_JOB' in frame_0:
                print(f"✅ ANNOTATION_NAMES_JOB : {frame_0['ANNOTATION_NAMES_JOB']}")
            else:
                print("⚠️ ANNOTATION_NAMES_JOB manquant")
                
            # Vérification des annotations
            if 'OBJECT_DETECTION_JOB' in frame_0 and 'annotations' in frame_0['OBJECT_DETECTION_JOB']:
                annotations = frame_0['OBJECT_DETECTION_JOB']['annotations']
                print(f"✅ {len(annotations)} annotations trouvées")
                
                if annotations:
                    first_ann = annotations[0]
                    print(f"🔍 Première annotation : {list(first_ann.keys())}")
                    
                    # Vérification des points
                    if 'boundingPoly' in first_ann and first_ann['boundingPoly']:
                        vertices = first_ann['boundingPoly'][0]['normalizedVertices']
                        print(f"✅ {len(vertices)} points trouvés dans le premier polygone")
                    else:
                        print("❌ Pas de points trouvés")
            else:
                print("❌ Pas d'annotations trouvées")
        else:
            print("❌ Frame 0 non trouvée")
    else:
        print("❌ Structure incorrecte")
    
    # 3. Comparaison avec le fichier de l'équipe Kili
    print("📊 3. Comparaison avec le fichier de l'équipe...")
    
    email_file = f"./json/{asset_name}_from_email.json"
    if os.path.exists(email_file):
        with open(email_file, "r") as f:
            email_data = json.load(f)
        
        # Comparaison des jsonResponse
        our_json_resp = full_data['latestLabel']['jsonResponse']
        email_json_resp = email_data['latestLabel']['jsonResponse']
        
        # Comparaison frame 0
        if '0' in our_json_resp and '0' in email_json_resp:
            our_frame_0 = our_json_resp['0']
            email_frame_0 = email_json_resp['0']
            
            our_keys = set(our_frame_0.keys())
            email_keys = set(email_frame_0.keys())
            
            print(f"🔑 Nos clés : {our_keys}")
            print(f"🔑 Clés email : {email_keys}")
            
            missing = email_keys - our_keys
            if missing:
                print(f"⚠️ Clés manquantes : {missing}")
            else:
                print("✅ Toutes les clés sont présentes")
                
            # Comparaison des annotations
            if 'OBJECT_DETECTION_JOB' in our_frame_0 and 'OBJECT_DETECTION_JOB' in email_frame_0:
                our_anns = our_frame_0['OBJECT_DETECTION_JOB'].get('annotations', [])
                email_anns = email_frame_0['OBJECT_DETECTION_JOB'].get('annotations', [])
                
                print(f"📊 Nos annotations : {len(our_anns)}")
                print(f"📊 Annotations email : {len(email_anns)}")
                
                if our_anns and email_anns:
                    our_points = len(our_anns[0]['boundingPoly'][0]['normalizedVertices'])
                    email_points = len(email_anns[0]['boundingPoly'][0]['normalizedVertices'])
                    
                    print(f"📍 Nos points : {our_points}")
                    print(f"📍 Points email : {email_points}")
                    
                    if our_points == email_points:
                        print("✅ Même nombre de points")
                    else:
                        print(f"⚠️ Différence de points : {email_points - our_points}")
        
    else:
        print("⚠️ Fichier de comparaison non trouvé")

except Exception as e:
    print(f"❌ Erreur : {e}")
    import traceback
    traceback.print_exc()

print("🏁 Test terminé")
