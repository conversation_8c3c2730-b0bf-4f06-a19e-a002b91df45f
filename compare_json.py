import json

def count_points(file_path):
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
            if data.get('jsonContent'):
                annotations = data['jsonContent'].get('annotations', [])
                return len(annotations)
            return 0
    except Exception as e:
        print(f"Error reading {file_path}: {str(e)}")
        return 0

file1 = "json/HydroFORM_Gen2_Demo_2_gr1_from_email.json"
file2 = "json/HydroFORM_Gen2_Demo_2_gr1_full.json"

points1 = count_points(file1)
points2 = count_points(file2)

print(f"Points in from_email: {points1}")
print(f"Points in full: {points2}")
print(f"Difference: {points1 - points2}") 