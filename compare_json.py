import json

def compare_points_detailed(file1_path, file2_path):
    """
    Compare les points entre deux fichiers JSON de Kili
    """
    try:
        # Chargement des fichiers
        with open(file1_path, 'r') as f:
            data1 = json.load(f)
        with open(file2_path, 'r') as f:
            data2 = json.load(f)

        print(f"🔍 Comparaison détaillée des points")
        print(f"📁 Fichier 1: {file1_path}")
        print(f"📁 Fichier 2: {file2_path}")
        print("=" * 60)

        # Extraction des jsonResponse
        json_resp1 = data1['latestLabel']['jsonResponse']
        json_resp2 = data2['latestLabel']['jsonResponse']

        # Comparaison des frames
        frames1 = set(json_resp1.keys())
        frames2 = set(json_resp2.keys())

        print(f"📊 Frames dans fichier 1: {sorted(frames1)}")
        print(f"📊 Frames dans fichier 2: {sorted(frames2)}")

        if frames1 != frames2:
            print(f"⚠️ Différence de frames!")
            print(f"   Manquantes dans fichier 2: {frames1 - frames2}")
            print(f"   Supplémentaires dans fichier 2: {frames2 - frames1}")
        else:
            print("✅ Mêmes frames dans les deux fichiers")

        # Analyse frame par frame
        common_frames = frames1 & frames2

        for frame_id in sorted(common_frames):
            print(f"\n🎬 FRAME {frame_id}:")

            frame1 = json_resp1[frame_id]
            frame2 = json_resp2[frame_id]

            # Comparaison des clés de la frame
            keys1 = set(frame1.keys())
            keys2 = set(frame2.keys())

            print(f"   🔑 Clés fichier 1: {keys1}")
            print(f"   🔑 Clés fichier 2: {keys2}")

            if keys1 != keys2:
                print(f"   ⚠️ Différence de clés!")
                print(f"      Manquantes dans fichier 2: {keys1 - keys2}")
                print(f"      Supplémentaires dans fichier 2: {keys2 - keys1}")

            # Analyse des annotations OBJECT_DETECTION_JOB
            if 'OBJECT_DETECTION_JOB' in frame1 and 'OBJECT_DETECTION_JOB' in frame2:
                obj1 = frame1['OBJECT_DETECTION_JOB']
                obj2 = frame2['OBJECT_DETECTION_JOB']

                if 'annotations' in obj1 and 'annotations' in obj2:
                    anns1 = obj1['annotations']
                    anns2 = obj2['annotations']

                    print(f"   📍 Annotations fichier 1: {len(anns1)}")
                    print(f"   📍 Annotations fichier 2: {len(anns2)}")

                    # Comparaison annotation par annotation
                    for i, (ann1, ann2) in enumerate(zip(anns1, anns2)):
                        if 'boundingPoly' in ann1 and 'boundingPoly' in ann2:
                            poly1 = ann1['boundingPoly']
                            poly2 = ann2['boundingPoly']

                            print(f"      🔸 Annotation {i+1}:")
                            print(f"         Polygones fichier 1: {len(poly1)}")
                            print(f"         Polygones fichier 2: {len(poly2)}")

                            # Comparaison des points du premier polygone
                            if poly1 and poly2:
                                points1 = poly1[0]['normalizedVertices']
                                points2 = poly2[0]['normalizedVertices']

                                print(f"         📍 Points fichier 1: {len(points1)}")
                                print(f"         📍 Points fichier 2: {len(points2)}")

                                if len(points1) != len(points2):
                                    print(f"         ⚠️ DIFFÉRENCE DE POINTS: {len(points1)} vs {len(points2)}")
                                    print(f"         📉 Différence: {len(points1) - len(points2)} points")
                                else:
                                    print(f"         ✅ Même nombre de points")

                                # Vérification des coordonnées des premiers points
                                if points1 and points2:
                                    p1_first = points1[0]
                                    p2_first = points2[0]
                                    print(f"         🎯 Premier point fichier 1: x={p1_first['x']}, y={p1_first['y']}")
                                    print(f"         🎯 Premier point fichier 2: x={p2_first['x']}, y={p2_first['y']}")

                                    if p1_first == p2_first:
                                        print(f"         ✅ Premiers points identiques")
                                    else:
                                        print(f"         ⚠️ Premiers points différents")

            # Vérification des champs spéciaux
            special_fields = ['ANNOTATION_JOB_COUNTER', 'ANNOTATION_NAMES_JOB']
            for field in special_fields:
                in1 = field in frame1
                in2 = field in frame2
                print(f"   🏷️ {field}:")
                print(f"      Fichier 1: {'✅' if in1 else '❌'}")
                print(f"      Fichier 2: {'✅' if in2 else '❌'}")

                if in1 and in2:
                    if frame1[field] == frame2[field]:
                        print(f"      ✅ Contenu identique")
                    else:
                        print(f"      ⚠️ Contenu différent")
                        print(f"         Fichier 1: {frame1[field]}")
                        print(f"         Fichier 2: {frame2[field]}")

        print("\n" + "=" * 60)
        print("🏁 Comparaison terminée")

    except Exception as e:
        print(f"❌ Erreur lors de la comparaison: {e}")
        import traceback
        traceback.print_exc()

# Fichiers à comparer
file1 = "json/HydroFORM_Gen2_Demo_2_gr1_from_email.json"
file2 = "json/HydroFORM_Gen2_Demo_2_gr1_full.json"

compare_points_detailed(file1, file2)