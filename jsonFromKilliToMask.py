import os
import json
import numpy as np
import matplotlib.pyplot as plt
from skimage.draw import polygon2mask
from matplotlib.colors import ListedColormap
from utils.kili_export import get_kili_json

# === Kili configuration
KILI_API_KEY = 'cma6sv06wf8tr015vcumzbjob/835a9982bff39b7bbc862e26cc2e2e10'
KILI_PROJECT_ID = 'cmb53asi847xh0171abma0b32'
asset_name = "HydroFORM_Gen2_Demo_2_gr1"

get_kili_json(asset_name=asset_name, kili_project_ID=KILI_PROJECT_ID, kili_api_key=KILI_API_KEY, export_format="full_asset")

# === Paramètres de l'image
WIDTH, HEIGHT = 512, 512  # Adapter à tes images

# === Chemin vers le fichier complet (comme celui de l'équipe Ki<PERSON>)
JSON_PATH = f"./json/{asset_name}_full.json"

# === Chargement du fichier complet
with open(JSON_PATH, "r", encoding="utf-8") as f:
    full_data = json.load(f)

print(f"📁 Fichier chargé : {JSON_PATH}")
print(f"🔍 Structure du fichier : {list(full_data.keys())}")

# Extraction du jsonResponse depuis la structure complète
annotations_by_frame = full_data["latestLabel"]["jsonResponse"]
print(f"📊 Frames trouvées : {list(annotations_by_frame.keys())}")

# === Filtrage : garder seulement les frames avec des annotations
annotations_by_frame = {
    fid: data for fid, data in annotations_by_frame.items()
    if any(job.get("annotations") for job in data.values())
}

# === Construction dynamique du CLASS_MAP
all_class_names = set()
for frame_data in annotations_by_frame.values():
    for job_data in frame_data.values():
        for ann in job_data.get("annotations", []):
            raw_name = ann["categories"][0]["name"]
            class_name = raw_name.split("_")[0].lower()
            all_class_names.add(class_name)

CLASS_MAP = {cls_name: idx + 1 for idx, cls_name in enumerate(sorted(all_class_names))}
print(f"🎯 Classes détectées : {CLASS_MAP}")

# === Dossier de sortie
OUTPUT_DIR = os.path.join("masques", asset_name)
os.makedirs(OUTPUT_DIR, exist_ok=True)

# === Génération des masques
frame_masks = {}
for frame_id, content in annotations_by_frame.items():
    frame = int(frame_id)
    temp_masks = {cls: np.zeros((HEIGHT, WIDTH), dtype=np.uint8) for cls in CLASS_MAP}

    for job_data in content.values():
        for ann in job_data.get("annotations", []):
            class_name = ann["categories"][0]["name"].split("_")[0].lower()
            if class_name not in CLASS_MAP:
                continue
            for poly in ann["boundingPoly"]:
                polygon_coords = [
                    (int(point["y"] * HEIGHT), int(point["x"] * WIDTH))
                    for point in poly["normalizedVertices"]
                ]
                polygon = np.array(polygon_coords)
                mask = polygon2mask((HEIGHT, WIDTH), polygon).astype(np.uint8)
                temp_masks[class_name] = np.logical_or(temp_masks[class_name], mask)

    final_mask = np.zeros((HEIGHT, WIDTH), dtype=np.uint8)
    for cls_name, binary_mask in temp_masks.items():
        final_mask[binary_mask] = CLASS_MAP[cls_name]

    frame_masks[frame] = final_mask
    print(f"✅ Frame {frame:04d} traitée")

# === Palette de couleurs dynamique
max_class = max(CLASS_MAP.values())
base_colors = [
    (1, 1, 1),         # 0 - fond
    (0.2, 0.4, 1.0),   # bleu
    (1.0, 0.3, 0.3),   # rouge
    (0.3, 1.0, 0.3),   # vert
    (1.0, 1.0, 0.0),   # jaune
    (0.6, 0.3, 1.0),   # violet
    (0.2, 0.9, 0.9),   # cyan
]
while len(base_colors) <= max_class:
    base_colors.append((np.random.rand(), np.random.rand(), np.random.rand()))
cmap = ListedColormap(base_colors[:max_class+1])

# === Sauvegarde PNG
for frame, mask in sorted(frame_masks.items()):
    output_path = os.path.join(OUTPUT_DIR, f"mask_frame_{frame:04d}.png")
    plt.imsave(output_path, mask, cmap=cmap, vmin=0, vmax=max_class)
    print(f"💾 Frame {frame:04d} sauvegardée → {output_path}")
