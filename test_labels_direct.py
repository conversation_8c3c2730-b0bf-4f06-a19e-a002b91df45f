#!/usr/bin/env python3
"""
Test direct pour récupérer tous les labels d'un asset
"""

from kili.client import Kili
import json
import os

print("🔍 Test direct des labels...")

try:
    # Configuration
    KILI_API_KEY = 'cma6sv06wf8tr015vcumzbjob/835a9982bff39b7bbc862e26cc2e2e10'
    KILI_PROJECT_ID = 'cmb52fpga1bd8015kewxihgop'
    asset_name = "HydroFORM_Gen2_Demo_2_gr1"
    
    # Connexion à Kili
    kili = Kili(api_key=KILI_API_KEY)
    print("✅ Connexion réussie")
    
    # 1. Récupération de l'asset pour obtenir son ID
    print("📥 Récupération de l'asset...")
    assets = kili.assets(
        project_id=KILI_PROJECT_ID,
        external_id_contains=[asset_name, asset_name+'.mp4']
    )
    
    if not assets:
        print("❌ Aucun asset trouvé")
        exit(1)
    
    asset = assets[0]
    asset_id = asset['id']
    print(f"✅ Asset trouvé : {asset_id}")
    
    # 2. Récupération de TOUS les labels pour cet asset
    print("📥 Récupération de tous les labels...")
    all_labels = kili.labels(
        project_id=KILI_PROJECT_ID,
        asset_id=asset_id
    )
    
    print(f"📊 Nombre total de labels : {len(all_labels)}")
    
    # 3. Analyse de chaque label
    best_label = None
    max_annotations = 0
    
    for i, label in enumerate(all_labels):
        print(f"\n📝 LABEL {i+1}:")
        print(f"   📅 Créé le : {label.get('createdAt', 'N/A')}")
        print(f"   👤 Auteur : {label.get('author', {}).get('email', 'N/A')}")
        print(f"   🏷️ Latest pour user : {label.get('isLatestLabelForUser', 'N/A')}")
        print(f"   🔄 Sent back to queue : {label.get('isSentBackToQueue', 'N/A')}")
        
        # Compter les annotations dans ce label
        json_resp = label.get('jsonResponse', {})
        total_annotations = 0
        
        for frame_id, frame_data in json_resp.items():
            if 'OBJECT_DETECTION_JOB' in frame_data:
                annotations = frame_data['OBJECT_DETECTION_JOB'].get('annotations', [])
                frame_count = len(annotations)
                total_annotations += frame_count
                if frame_count > 0:
                    print(f"      🎬 Frame {frame_id}: {frame_count} annotations")
        
        print(f"   📊 Total annotations : {total_annotations}")
        
        # Garder le label avec le plus d'annotations
        if total_annotations > max_annotations:
            max_annotations = total_annotations
            best_label = label
            print(f"   🏆 NOUVEAU MEILLEUR LABEL !")
    
    # 4. Utiliser le meilleur label
    if best_label:
        print(f"\n🎯 LABEL SÉLECTIONNÉ :")
        print(f"   📊 {max_annotations} annotations totales")
        print(f"   📅 Créé le : {best_label.get('createdAt', 'N/A')}")
        print(f"   👤 Auteur : {best_label.get('author', {}).get('email', 'N/A')}")
        
        # Construction du fichier complet
        author_info = best_label.get('author', {})
        if author_info:
            firstname = author_info.get('firstname', '')
            lastname = author_info.get('lastname', '')
            full_name = f"{firstname} {lastname}".strip()
            author_info['name'] = full_name
        
        full_asset_data = {
            "content": asset.get('content', ''),
            "externalId": asset.get('externalId', ''),
            "id": asset.get('id', ''),
            "jsonContent": asset.get('jsonContent'),
            "jsonMetadata": asset.get('jsonMetadata', {}),
            "latestLabel": {
                "author": author_info,
                "createdAt": best_label.get('createdAt', ''),
                "isLatestLabelForUser": best_label.get('isLatestLabelForUser', False),
                "isSentBackToQueue": best_label.get('isSentBackToQueue', False),
                "jsonResponse": best_label.get('jsonResponse', {})
            }
        }
        
        # Sauvegarde
        os.makedirs("json", exist_ok=True)
        output_file = f"./json/{asset_name}_best_label.json"
        with open(output_file, "w") as outfile:
            json.dump(full_asset_data, outfile, indent=4)
        
        print(f"✅ Fichier sauvegardé : {output_file}")
        
        # Analyse du résultat
        json_resp = best_label['jsonResponse']
        print(f"\n📊 RÉSULTAT FINAL :")
        for frame_id in sorted(json_resp.keys()):
            frame_data = json_resp[frame_id]
            if 'OBJECT_DETECTION_JOB' in frame_data:
                annotations = frame_data['OBJECT_DETECTION_JOB'].get('annotations', [])
                print(f"   🎬 Frame {frame_id}: {len(annotations)} annotations")
    else:
        print("❌ Aucun label avec annotations trouvé")

except Exception as e:
    print(f"❌ Erreur : {e}")
    import traceback
    traceback.print_exc()
