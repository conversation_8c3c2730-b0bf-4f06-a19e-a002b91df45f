#!/usr/bin/env python3
"""
Test de l'export filtré pour ne garder que les frames annotées
"""

print("🎯 Test de l'export filtré (frames annotées seulement)...")

try:
    from utils.kili_export import get_kili_json
    
    # Configuration
    KILI_API_KEY = 'cma6sv06wf8tr015vcumzbjob/835a9982bff39b7bbc862e26cc2e2e10'
    KILI_PROJECT_ID = 'cmb52fpga1bd8015kewxihgop'
    asset_name = "HydroFORM_Gen2_Demo_2_gr1"
    
    print("📥 Export avec filtrage des frames...")
    
    # Export avec la nouvelle logique filtrée
    full_data = get_kili_json(
        asset_name=asset_name, 
        kili_project_ID=KILI_PROJECT_ID, 
        kili_api_key=KILI_API_KEY,
        export_format="full_asset"
    )
    
    print("✅ Export terminé !")
    
    # Analyse du résultat
    json_resp = full_data['latestLabel']['jsonResponse']
    
    print(f"\n📊 RÉSULTAT FILTRÉ :")
    print(f"   🎬 Nombre de frames : {len(json_resp)}")
    print(f"   📋 Frames : {sorted(json_resp.keys(), key=int)}")
    
    # Compter les annotations
    total_annotations = 0
    for frame_id in sorted(json_resp.keys(), key=int):
        frame_data = json_resp[frame_id]
        if 'OBJECT_DETECTION_JOB' in frame_data:
            annotations = frame_data['OBJECT_DETECTION_JOB'].get('annotations', [])
            total_annotations += len(annotations)
            print(f"   🎬 Frame {frame_id}: {len(annotations)} annotations")
    
    print(f"\n📊 TOTAL : {total_annotations} annotations")
    
    # Comparaison avec le fichier de l'équipe
    try:
        import json
        with open(f"./json/{asset_name}_from_email.json", "r") as f:
            email_data = json.load(f)
        
        email_json_resp = email_data['latestLabel']['jsonResponse']
        email_frames = len(email_json_resp)
        email_total = 0
        
        for frame_data in email_json_resp.values():
            if 'OBJECT_DETECTION_JOB' in frame_data:
                email_total += len(frame_data['OBJECT_DETECTION_JOB'].get('annotations', []))
        
        print(f"\n🔍 COMPARAISON AVEC FICHIER ÉQUIPE :")
        print(f"   📧 Équipe - Frames : {email_frames}, Annotations : {email_total}")
        print(f"   🔄 Notre  - Frames : {len(json_resp)}, Annotations : {total_annotations}")
        
        frames_match = len(json_resp) == email_frames
        annotations_match = total_annotations >= email_total
        
        print(f"\n🎯 RÉSULTAT :")
        print(f"   🎬 Frames : {'✅' if frames_match else '❌'} ({len(json_resp)} vs {email_frames})")
        print(f"   📊 Annotations : {'✅' if annotations_match else '❌'} ({total_annotations} vs {email_total})")
        
        if frames_match and annotations_match:
            print("   🎉 SUCCÈS COMPLET ! Votre export est maintenant équivalent !")
        elif frames_match:
            print("   ✅ Bon nombre de frames, mais annotations manquantes")
        elif annotations_match:
            print("   ✅ Bonnes annotations, mais trop de frames")
        else:
            print("   ⚠️ Encore des différences à corriger")
            
    except FileNotFoundError:
        print("   ⚠️ Fichier de comparaison non trouvé")

except Exception as e:
    print(f"❌ Erreur : {e}")
    import traceback
    traceback.print_exc()

print("\n🏁 Test terminé")
