#!/usr/bin/env python3
"""
Test simple pour vérifier l'export et la structure
"""

import os
import json

print("🔄 Test de l'export Kili...")

try:
    # Import de la fonction
    from utils.kili_export import get_kili_json
    
    # Configuration
    KILI_API_KEY = 'cma6sv06wf8tr015vcumzbjob/835a9982bff39b7bbc862e26cc2e2e10'
    KILI_PROJECT_ID = 'cmb52fpga1bd8015kewxihgop'
    asset_name = "HydroFORM_Gen2_Demo_2_gr1"
    
    print("📥 Export depuis Kili...")
    
    # Export avec format complet
    full_data = get_kili_json(
        asset_name=asset_name, 
        kili_project_ID=KILI_PROJECT_ID, 
        kili_api_key=KILI_API_KEY,
        export_format="full_asset"
    )
    
    print("✅ Export terminé !")
    
    # Vérification des fichiers créés
    file_full = f"./json/{asset_name}_full.json"
    file_standard = f"./json/{asset_name}.json"
    
    if os.path.exists(file_full):
        print(f"✅ Fichier complet créé : {file_full}")
    else:
        print(f"❌ Fichier complet manquant : {file_full}")
    
    if os.path.exists(file_standard):
        print(f"✅ Fichier standard créé : {file_standard}")
    else:
        print(f"❌ Fichier standard manquant : {file_standard}")
    
    # Vérification de la structure
    print("\n🔍 Vérification de la structure...")
    
    if 'latestLabel' in full_data:
        print("✅ latestLabel trouvé")
        
        if 'jsonResponse' in full_data['latestLabel']:
            print("✅ jsonResponse trouvé")
            
            json_resp = full_data['latestLabel']['jsonResponse']
            frames = list(json_resp.keys())
            print(f"📊 Frames : {frames}")
            
            if '0' in json_resp:
                frame_0 = json_resp['0']
                print(f"🔍 Contenu frame 0 : {list(frame_0.keys())}")
                
                # Vérification des annotations
                if 'OBJECT_DETECTION_JOB' in frame_0:
                    obj_job = frame_0['OBJECT_DETECTION_JOB']
                    if 'annotations' in obj_job:
                        annotations = obj_job['annotations']
                        print(f"✅ {len(annotations)} annotations trouvées")
                        
                        if annotations:
                            first_ann = annotations[0]
                            if 'boundingPoly' in first_ann and first_ann['boundingPoly']:
                                points = first_ann['boundingPoly'][0]['normalizedVertices']
                                print(f"✅ {len(points)} points dans la première annotation")
                            else:
                                print("❌ Pas de points trouvés")
                    else:
                        print("❌ Pas d'annotations trouvées")
                else:
                    print("❌ OBJECT_DETECTION_JOB non trouvé")
            else:
                print("❌ Frame 0 non trouvée")
        else:
            print("❌ jsonResponse non trouvé")
    else:
        print("❌ latestLabel non trouvé")
    
    print("\n📊 Comparaison avec le fichier de l'équipe...")
    
    # Comparaison avec le fichier de l'équipe
    email_file = f"./json/{asset_name}_from_email.json"
    if os.path.exists(email_file):
        with open(email_file, "r") as f:
            email_data = json.load(f)
        
        print("✅ Fichier de l'équipe trouvé")
        
        # Comparaison des structures principales
        our_keys = set(full_data.keys())
        email_keys = set(email_data.keys())
        
        print(f"🔑 Nos clés : {our_keys}")
        print(f"🔑 Clés équipe : {email_keys}")
        
        if our_keys == email_keys:
            print("✅ Structures principales identiques")
        else:
            missing = email_keys - our_keys
            extra = our_keys - email_keys
            if missing:
                print(f"⚠️ Clés manquantes : {missing}")
            if extra:
                print(f"ℹ️ Clés supplémentaires : {extra}")
    else:
        print("⚠️ Fichier de l'équipe non trouvé")

except Exception as e:
    print(f"❌ Erreur : {e}")
    import traceback
    traceback.print_exc()

print("\n🏁 Test terminé")
