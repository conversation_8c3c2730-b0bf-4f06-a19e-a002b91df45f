#!/usr/bin/env python3
"""
Comparaison simple entre les deux fichiers JSON
"""

import json

def simple_compare():
    try:
        # Chargement des fichiers
        with open("json/HydroFORM_Gen2_Demo_2_gr1_from_email.json", 'r') as f:
            email_data = json.load(f)
        
        with open("json/HydroFORM_Gen2_Demo_2_gr1_full.json", 'r') as f:
            our_data = json.load(f)
        
        print("📊 COMPARAISON SIMPLE")
        print("=" * 50)
        
        # Extraction des jsonResponse
        email_json = email_data['latestLabel']['jsonResponse']
        our_json = our_data['latestLabel']['jsonResponse']
        
        # Comparaison frame par frame
        email_frames = set(email_json.keys())
        our_frames = set(our_json.keys())
        
        print(f"🎬 Frames email: {sorted(email_frames)}")
        print(f"🎬 Frames notre: {sorted(our_frames)}")
        
        common_frames = email_frames & our_frames
        
        total_email_annotations = 0
        total_our_annotations = 0
        
        for frame_id in sorted(common_frames):
            email_frame = email_json[frame_id]
            our_frame = our_json[frame_id]
            
            # Compter les annotations
            email_anns = 0
            our_anns = 0
            
            if 'OBJECT_DETECTION_JOB' in email_frame:
                email_anns = len(email_frame['OBJECT_DETECTION_JOB'].get('annotations', []))
            
            if 'OBJECT_DETECTION_JOB' in our_frame:
                our_anns = len(our_frame['OBJECT_DETECTION_JOB'].get('annotations', []))
            
            total_email_annotations += email_anns
            total_our_annotations += our_anns
            
            status = "✅" if email_anns == our_anns else "❌"
            print(f"{status} Frame {frame_id}: Email={email_anns}, Notre={our_anns}")
            
            # Vérifier les champs spéciaux
            has_counter_email = 'ANNOTATION_JOB_COUNTER' in email_frame
            has_counter_our = 'ANNOTATION_JOB_COUNTER' in our_frame
            has_names_email = 'ANNOTATION_NAMES_JOB' in email_frame
            has_names_our = 'ANNOTATION_NAMES_JOB' in our_frame
            
            if frame_id == '0':  # Détails pour la première frame
                print(f"   📊 ANNOTATION_JOB_COUNTER: Email={has_counter_email}, Notre={has_counter_our}")
                print(f"   🏷️ ANNOTATION_NAMES_JOB: Email={has_names_email}, Notre={has_names_our}")
        
        print("=" * 50)
        print(f"📊 TOTAL ANNOTATIONS:")
        print(f"   Email: {total_email_annotations}")
        print(f"   Notre: {total_our_annotations}")
        print(f"   Différence: {total_email_annotations - total_our_annotations}")
        
        if total_email_annotations == total_our_annotations:
            print("✅ MÊME NOMBRE D'ANNOTATIONS TOTAL")
        else:
            print("❌ NOMBRE D'ANNOTATIONS DIFFÉRENT")
            print(f"   Il nous manque {total_email_annotations - total_our_annotations} annotations")
        
    except FileNotFoundError as e:
        print(f"❌ Fichier non trouvé: {e}")
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    simple_compare()
