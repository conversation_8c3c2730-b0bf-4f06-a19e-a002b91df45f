#!/usr/bin/env python3
"""
Test de la fonction corrigée pour récupérer le format complet
"""

from utils.kili_export import get_kili_json

# Configuration Kili
KILI_API_KEY = 'cma6sv06wf8tr015vcumzbjob/835a9982bff39b7bbc862e26cc2e2e10'
KILI_PROJECT_ID = 'cmb52fpga1bd8015kewxihgop'
asset_name = "HydroFORM_Gen2_Demo_2_gr1"

print("🔄 Test de récupération du format complet (version corrigée)...")

try:
    # Test du nouveau format complet
    full_data = get_kili_json(
        asset_name=asset_name, 
        kili_project_ID=KILI_PROJECT_ID, 
        kili_api_key=KILI_API_KEY,
        export_format="full_asset"
    )
    
    print("✅ Export complet réussi !")
    print(f"📁 Fichier créé : ./json/{asset_name}_full.json")
    
    # Vérification de la structure
    if 'latestLabel' in full_data and 'jsonResponse' in full_data['latestLabel']:
        print("✅ Structure latestLabel.jsonResponse trouvée")
        
        # Vérification de l'auteur
        author = full_data['latestLabel'].get('author', {})
        if 'name' in author:
            print(f"✅ Nom de l'auteur : {author['name']}")
        else:
            print("⚠️ Nom de l'auteur manquant")
        
        json_response = full_data['latestLabel']['jsonResponse']
        if '0' in json_response and 'OBJECT_DETECTION_JOB' in json_response['0']:
            annotations = json_response['0']['OBJECT_DETECTION_JOB'].get('annotations', [])
            print(f"✅ {len(annotations)} annotations trouvées dans la frame 0")
            
            # Vérification des champs supplémentaires
            frame_0 = json_response['0']
            if 'ANNOTATION_JOB_COUNTER' in frame_0:
                print("✅ ANNOTATION_JOB_COUNTER trouvé")
                print(f"   Contenu : {frame_0['ANNOTATION_JOB_COUNTER']}")
            else:
                print("⚠️ ANNOTATION_JOB_COUNTER manquant")
                
            if 'ANNOTATION_NAMES_JOB' in frame_0:
                print("✅ ANNOTATION_NAMES_JOB trouvé")
                print(f"   Contenu : {frame_0['ANNOTATION_NAMES_JOB']}")
            else:
                print("⚠️ ANNOTATION_NAMES_JOB manquant")
        else:
            print("❌ Structure d'annotations non trouvée")
    else:
        print("❌ Structure latestLabel.jsonResponse non trouvée")

except Exception as e:
    print(f"❌ Erreur lors de l'export : {e}")
    import traceback
    traceback.print_exc()
