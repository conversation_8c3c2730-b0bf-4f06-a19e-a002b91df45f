import os
import json
import numpy as np
import matplotlib.pyplot as plt
from skimage.draw import polygon2mask
from matplotlib.colors import ListedColormap

# 📁 Fichier source
JSON_PATH = "./json/HydroFORM_Gen2_Demo_2_gr1_debug.json"
OUTPUT_DIR = os.path.join("masques", "NoAPI", os.path.splitext(os.path.basename(JSON_PATH))[0])
WIDTH, HEIGHT = 512, 512

# === Construction dynamique du CLASS_MAP
with open(JSON_PATH, "r") as f:
    data = json.load(f)

all_class_names = set()
for asset in data:
    label = asset.get("latestLabel", {})
    annotations_by_frame = label.get("jsonResponse", {})
    for frame_data in annotations_by_frame.values():
        for job_data in frame_data.values():
            for ann in job_data.get("annotations", []):
                raw_name = ann["categories"][0]["name"]
                class_name = raw_name.split("_")[0].lower()
                all_class_names.add(class_name)

CLASS_MAP = {cls_name: idx + 1 for idx, cls_name in enumerate(sorted(all_class_names))}
print(f"🎯 Classes détectées : {CLASS_MAP}")

os.makedirs(OUTPUT_DIR, exist_ok=True)

frame_masks = {}

for asset in data:
    asset_id = asset.get("externalId", "unknown")
    label = asset.get("latestLabel", {})
    annotations_by_frame = label.get("jsonResponse", {})

    for frame_id, content in annotations_by_frame.items():
        frame = int(frame_id)
        for job_name, job_data in content.items():
            if "annotations" not in job_data:
                continue
            for ann in job_data["annotations"]:
                class_name = ann["categories"][0]["name"].split("_")[0].lower()
                class_value = CLASS_MAP.get(class_name, 0)

                if class_value == 0:
                    print(f"⚠️ Classe inconnue ignorée : {class_name}")
                    continue

                for poly in ann["boundingPoly"]:
                    polygon_coords = [
                        (int(point["y"] * HEIGHT), int(point["x"] * WIDTH))
                        for point in poly["normalizedVertices"]
                    ]
                    polygon = np.array(polygon_coords)
                    mask = polygon2mask((HEIGHT, WIDTH), polygon).astype(np.uint8) * class_value

                    if frame not in frame_masks:
                        frame_masks[frame] = np.zeros((HEIGHT, WIDTH), dtype=np.uint8)

                    frame_masks[frame] = np.where(
                        (mask > 0) & (frame_masks[frame] == 0),
                        mask,
                        frame_masks[frame]
                    )
        print(f"✅ {asset_id} | Frame {frame} traité")

# 🎨 Palette de couleurs
max_class = max(CLASS_MAP.values())
base_colors = [
    (1, 1, 1),         # 0 - fond
    (0.2, 0.4, 1.0),   # bleu
    (1.0, 0.3, 0.3),   # rouge
    (0.3, 1.0, 0.3),   # vert
    (1.0, 1.0, 0.0),   # jaune
    (0.6, 0.3, 1.0),   # violet
    (0.2, 0.9, 0.9),   # cyan
]
while len(base_colors) <= max_class:
    base_colors.append((np.random.rand(), np.random.rand(), np.random.rand()))
cmap = ListedColormap(base_colors[:max_class+1])

# 💾 Sauvegarde
for frame, mask in sorted(frame_masks.items()):
    output_path = os.path.join(OUTPUT_DIR, f"mask_frame_{frame:04d}.png")
    plt.imsave(output_path, mask, cmap=cmap, vmin=0, vmax=max_class)
    print(f"💾 Frame {frame} sauvegardé → {output_path}")
